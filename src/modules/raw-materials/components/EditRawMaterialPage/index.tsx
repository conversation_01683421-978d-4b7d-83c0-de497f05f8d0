import { Building, DollarSign, Hash, Package, Tag } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Product } from "src/modules/product/service/model/product";
import useEditRawMaterialPage from "./use-edit-raw-material-page";

interface EditRawMaterialFormProps {
	product: Product;
}

export default function EditRawMaterialForm({ product }: EditRawMaterialFormProps) {
	const { product: productService } = useService();
	const { form, handleSubmit, isPending, brands, measurementUnits } =
		useEditRawMaterialPage({ product });

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="name"
							children={({ FSTextField }) => (
								<FSTextField
									label="Nombre"
									placeholder="Nombre de la materia prima"
									prefixComponent={<Tag size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="commercialName"
							validators={{
								onChangeAsyncDebounceMs: 500,
								onChangeAsync: async ({ value }) => {
									if (
										!value ||
										value.trim() === "" ||
										value === product.commercialName
									) {
										return undefined;
									}
									try {
										await AppRuntime.runPromise(
											productService.validateCommercialName(value),
										);
										return undefined;
									} catch (e) {
										return [{ message: "El nombre comercial ya existe" }];
									}
								},
							}}
							children={({ FSTextField }) => (
								<FSTextField
									label="Nombre Comercial"
									placeholder="Nombre comercial de la materia prima"
									prefixComponent={<Building size={16} />}
								/>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="code"
							validators={{
								onChangeAsyncDebounceMs: 500,
								onChangeAsync: async ({ value }) => {
									if (
										!value ||
										value.trim() === "" ||
										value === product.code
									) {
										return undefined;
									}
									try {
										await AppRuntime.runPromise(
											productService.validateCode(value),
										);
										return undefined;
									} catch (e) {
										return [{ message: "El código ya existe" }];
									}
								},
							}}
							children={({ FSTextField }) => (
								<FSTextField
									label="Código"
									placeholder="Código de la materia prima"
									prefixComponent={<Hash size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="skuCode"
							validators={{
								onChangeAsyncDebounceMs: 500,
								onChangeAsync: async ({ value }) => {
									if (
										!value ||
										value.trim() === "" ||
										value === product.skuCode
									) {
										return undefined;
									}
									try {
										await AppRuntime.runPromise(
											productService.validateSKUCode(value),
										);
										return undefined;
									} catch (e) {
										return [{ message: "El código SKU ya existe" }];
									}
								},
							}}
							children={({ FSTextField }) => (
								<FSTextField
									label="Código SKU"
									placeholder="Código SKU de la materia prima"
									prefixComponent={<Package size={16} />}
								/>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="brandID"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Marca"
									placeholder="Seleccionar marca"
									options={brands.map((brand) => ({
										value: brand.id,
										label: brand.name,
									}))}
								/>
							)}
						/>
						<form.AppField
							name="measurementUnitID"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Unidad de Medida"
									placeholder="Seleccionar unidad de medida"
									options={measurementUnits.map((unit) => ({
										value: unit.id,
										label: unit.name,
									}))}
								/>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="costPrice"
							children={({ FSTextField }) => (
								<FSTextField
									label="Precio de Costo"
									placeholder="0.00"
									type="number"
									prefixComponent={<DollarSign size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="state"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Estado"
									placeholder="Seleccionar estado"
									options={[
										{ value: "ACTIVE", label: "Activo" },
										{ value: "INACTIVE", label: "Inactivo" },
									]}
								/>
							)}
						/>
					</div>

					<form.AppField
						name="description"
						children={({ FSTextAreaField }) => (
							<FSTextAreaField
								label="Descripción"
								placeholder="Descripción de la materia prima (opcional)"
							/>
						)}
					/>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="canBeSold"
							children={({ FSToggleField }) => (
								<FSToggleField label="Se puede vender" />
							)}
						/>
						<form.AppField
							name="canBePurchased"
							children={({ FSToggleField }) => (
								<FSToggleField label="Se puede comprar" />
							)}
						/>
					</div>
				</fieldset>
				<div className="flex justify-end gap-4">
					<a href="/admin/products/raw-materials" className="btn btn-ghost">
						Cancelar
					</a>
					<form.Subscribe
						selector={(state) => [state.canSubmit, state.isSubmitting]}
						children={([canSubmit, isSubmitting]) => (
							<button
								type="submit"
								className="btn btn-primary"
								disabled={!canSubmit}
							>
								{isSubmitting ? (
									<span className="loading loading-spinner loading-sm" />
								) : (
									"Actualizar Materia Prima"
								)}
							</button>
						)}
					/>
				</div>
			</form.AppForm>
		</form>
	);
}
