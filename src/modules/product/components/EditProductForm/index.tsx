import { useQuery } from "@tanstack/react-query";
import { useService } from "src/config/context/serviceProvider";
import { productOptionsById } from "src/modules/product/hooks/product-options";
import EditProductForm from "./EditProductForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditProductModal({ isOpen, setIsOpen, id }: Props) {
	const service = useService();
	const { data: product, isLoading } = useQuery(
		productOptionsById(service, id),
	);

	if (!isOpen) return null;

	if (isLoading) {
		return (
			<div className="modal modal-open">
				<div className="modal-box">
					<div className="flex justify-center">
						<span className="loading loading-spinner loading-lg" />
					</div>
				</div>
			</div>
		);
	}

	if (!product) {
		return (
			<div className="modal modal-open">
				<div className="modal-box">
					<h3 className="font-bold text-lg">Error</h3>
					<p>No se pudo cargar el producto</p>
					<div className="modal-action">
						<button
							type="button"
							className="btn"
							onClick={() => setIsOpen(false)}
						>
							Cerrar
						</button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<EditProductForm isOpen={isOpen} setIsOpen={setIsOpen} product={product} />
	);
}
