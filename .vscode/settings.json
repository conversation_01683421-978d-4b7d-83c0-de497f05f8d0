{
  "emeraldwalk.runonsave": {
    "commands": [
      {
        "match": "\\.(ts|tsx|js|jsx|html)$",
        "cmd": "bunx @biomejs/biome lint ${file} --apply-unsafe"
      }
    ]
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
}