import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { CreateProduct, Product } from "../service/model/product";
import { productOptions } from "./product-options";

export default function useCreateProduct() {
	const service = useService();
	const { product } = service;
	const queryClient = useQueryClient();
	const queryKey = productOptions(service).queryKey;

	return useMutation({
		mutationFn: (newProduct: CreateProduct) =>
			AppRuntime.runPromise(product.create(newProduct)),
		onMutate: async (newProduct) => {
			await queryClient.cancelQueries({ queryKey });
			const previousProducts = queryClient.getQueryData<Product[]>(queryKey);

			queryClient.setQueryData<Product[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						draft.push({
							id: "temp-id",
							...newProduct,
							imageURL: newProduct.imageURL || null,
							description: newProduct.description || null,
							costPrice: newProduct.costPrice || null,
							createdAt: new Date().toISOString(),
							updatedAt: new Date().toISOString(),
							deletedAt: null,
						});
					}
				}),
			);

			return { previousProducts };
		},
		onError: (_, __, context) => {
			if (context?.previousProducts) {
				queryClient.setQueryData(queryKey, context.previousProducts);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
