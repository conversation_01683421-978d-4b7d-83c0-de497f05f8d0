import { toast } from "react-toastify";
import CloseModal from "src/core/components/CloseModal";
import { cn } from "src/core/utils/classes";
import useDeleteCategory from "../../hooks/use-delete-category";
import type { Category } from "../../service/model/category";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	category: Category;
}

export default function DeleteCategoryModal({ isOpen, setIsOpen, category }: Props) {
	const { mutate } = useDeleteCategory();

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar categoría</h3>
				<p>¿Estás seguro de que quieres eliminar esta categoría?</p>
				<p className="mt-2 text-gray-600 text-sm">
					Categoría: {category.name} ({category.code})
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={() => {
							mutate(category.id, {
								onSuccess: () => {
									toast.success("Categoría eliminada");
									setIsOpen(false);
								},
								onError: (error) => {
									console.log(error);
									toast.error("Error al eliminar categoría");
								},
							});
						}}
					>
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
