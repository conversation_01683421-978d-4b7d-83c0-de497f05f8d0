import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { brandOptions } from "~/modules/brand/hooks/brand-options";
import { CategoryCode } from "~/modules/category";
import { categoryOptions } from "~/modules/category/hooks/category-options";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import useCreateProduct from "~/modules/product/hooks/use-create-product";
import { CreateProductionDeviceSchema } from "./schema";


const defaultValues = {
	name: "",
	commercialName: "",
	code: "",
	skuCode: "",
	brandID: "",
	measurementUnitID: "",
	state: "ACTIVE",
	description: "",
	canBeSold: false,
	canBePurchased: true,
	costPrice: 0,
} as CreateProductionDeviceSchema;

export default function useCreateProductionDevicePage() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service)); 
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Find the production devices category
	const productionDeviceCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTION_DEVICES,
	);

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateProductionDeviceSchema,
		},
		onSubmit: ({ value }) => {
			// This will be handled by the handleSubmit function
		},
	});

	const handleSubmit = () => {
		// Validate form first
		form.handleSubmit();

	

		if (!productionDeviceCategory) {
			toast.error(
				"No se pudo encontrar la categoría de dispositivos de producción",
			);
			return;
		}

		// Get form values
		const formValues = form.state.values;

		// Create the product with production devices category
		mutate(
			{
				name: formValues.name,
				commercialName: formValues.commercialName,
				code: formValues.code,
				skuCode: formValues.skuCode,
				brandID: formValues.brandID,
				measurementUnitID: formValues.measurementUnitID,
				categoryID: productionDeviceCategory.id,
				state: formValues.state,
				description: formValues.description || undefined,
				canBeSold: formValues.canBeSold,
				canBePurchased: formValues.canBePurchased,
				costPrice: formValues.costPrice
			},
			{
				onSuccess: () => {
					toast.success("Dispositivo de producción creado exitosamente");
					navigate({ to: "/admin/products/production-devices" });
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			},
		);
	};

	return {
		form,
		handleSubmit,
		isPending,
		brands,
		measurementUnits,
		productionDeviceCategory,
	};
}
