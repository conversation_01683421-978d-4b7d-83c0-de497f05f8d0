{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["vite.config.ts", "src/config/css/app.css"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": "error"}, "correctness": {"noChildrenProp": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}