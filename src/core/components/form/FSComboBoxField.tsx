import { Undefined } from "effect/Schema";
import ComboBox from "../ComboBox";
import { useFieldContext } from "./form";

type Option = { value: string | number; label: string };

interface Props {
	label: string;
	placeholder?: string | undefined;
	options: Option[];
	isNumber?: boolean;
	isMultiple?: boolean;
	isLoading?: boolean;
}

type ComboBoxField = Omit<Props, "label" | "isMultiple">;

export default function FSComboBoxField({
	label,
	placeholder,
	options,
	isNumber = false,
	isMultiple = false,
}: Props) {
	return (
		<>
			<label htmlFor="combobox" className="label">
				{label}
			</label>
			{isMultiple ? (
				<MultipleComboBoxField
					placeholder={placeholder}
					options={options}
					isNumber={isNumber}
				/>
			) : (
				<SingleComboBoxField placeholder={placeholder} options={options} />
			)}
		</>
	);
}

function SingleComboBoxField({
	placeholder,
	options,
	isLoading = false,
}: ComboBoxField) {
	const field = useFieldContext<number | string>();

	return (
		<ComboBox
			isMultiple={false}
			options={options}
			onChange={(selectedItem) => field.handleChange(selectedItem?.value || "")}
			placeholder={placeholder}
			isLoading={isLoading}
			defaultSelected={
				options.find((option) => option.value === field.state.value) || null
			}
		/>
	);
}

function MultipleComboBoxField({
	placeholder,
	options,
	isLoading = false,
}: ComboBoxField) {
	const field = useFieldContext<(number | string)[]>();

	return (
		<ComboBox
			isMultiple
			options={options}
			onChange={(selectedItems) =>
				field.handleChange(selectedItems.map((item) => item.value))
			}
			placeholder={placeholder}
			isLoading={isLoading}
			defaultSelected={
				field.state.value
					? options.filter((o) => field.state.value.includes(o.value))
					: []
			}
		/>
	);
}
