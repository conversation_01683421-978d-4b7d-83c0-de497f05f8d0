import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import { categoryOptions } from "src/modules/category/hooks/category-options";
import { CategoryCode } from "src/modules/category/service/model/category";
import { measurementUnitOptions } from "src/modules/measurement-unit/hooks/measurement-unit-options";
import { useCreateProduct } from "src/modules/product";
import { CreateRawMaterialSchema } from "./schema";

const defaultValues = {
	name: "",
	commercialName: "",
	code: "",
	skuCode: "",
	brandID: "",
	measurementUnitID: "",
	categoryIDs: [],
	state: "ACTIVE",
	description: "",
	canBeSold: false,
	canBePurchased: true,
	costPrice: "",
} as CreateRawMaterialSchema;

export default function useCreateRawMaterialPage() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);
	const { data: categories = [] } = useQuery(categoryOptions(service));

	// Find the raw materials category
	const rawMaterialCategory = categories.find(
		(cat) => cat.code === CategoryCode.RAW_MATERIALS,
	);

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateRawMaterialSchema,
		},
		onSubmit: ({ value }) => {
			// This will be handled by the handleSubmit function
		},
	});

	const handleSubmit = () => {
		// Validate form first
		form.handleSubmit();

		// Check if form is valid
		if (form.state.errors.length > 0) {
			toast.error("Por favor corrige los errores en el formulario");
			return;
		}

		if (!rawMaterialCategory) {
			toast.error("No se pudo encontrar la categoría de materias primas");
			return;
		}

		// Get form values
		const formValues = form.state.values;

		// Create the product with raw materials category
		mutate(
			{
				name: formValues.name,
				commercialName: formValues.commercialName,
				code: formValues.code,
				skuCode: formValues.skuCode,
				brandID: formValues.brandID,
				measurementUnitID: formValues.measurementUnitID,
				categoryIDs: rawMaterialCategory?.id
					? [...formValues.categoryIDs, rawMaterialCategory?.id]
					: formValues.categoryIDs,
				state: formValues.state,
				description: formValues.description || undefined,
				canBeSold: formValues.canBeSold,
				canBePurchased: formValues.canBePurchased,
				costPrice: formValues.costPrice
					? Number.parseFloat(formValues.costPrice)
					: undefined,
			},
			{
				onSuccess: () => {
					toast.success("Materia prima creada exitosamente");
					navigate({ to: "/admin/products/raw-materials" });
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			},
		);
	};

	return {
		form,
		handleSubmit,
		isPending,
		brands,
		measurementUnits,
		rawMaterialCategory,
	};
}
