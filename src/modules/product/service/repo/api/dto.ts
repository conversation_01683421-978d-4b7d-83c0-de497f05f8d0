import { Schema } from "effect";
import { CreateProduct, Product, UpdateProduct } from "../../model/product";

export const ProductApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	image_url: Schema.NullOr(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.NullOr(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.NullOr(Schema.Number),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ProductFromApi = Schema.transform(ProductApi, Product, {
	strict: true,
	decode: (productApi) => ({
		...productApi,
		imageURL: productApi.image_url,
		commercialName: productApi.commercial_name,
		skuCode: productApi.sku_code,
		measurementUnitID: productApi.measurement_unit_id,
		categoryIDs: productApi.category_ids,
		brandID: productApi.brand_id,
		canBeSold: productApi.can_be_sold,
		canBePurchased: productApi.can_be_purchased,
		costPrice: productApi.cost_price,
		createdAt: productApi.created_at,
		updatedAt: productApi.updated_at,
		deletedAt: productApi.deleted_at,
	}),
	encode: (product) => ({
		...product,
		image_url: product.imageURL,
		commercial_name: product.commercialName,
		sku_code: product.skuCode,
		measurement_unit_id: product.measurementUnitID,
		category_ids: product.categoryIDs,
		brand_id: product.brandID,
		can_be_sold: product.canBeSold,
		can_be_purchased: product.canBePurchased,
		cost_price: product.costPrice,
		created_at: product.createdAt,
		updated_at: product.updatedAt,
		deleted_at: product.deletedAt,
	}),
});

export const ProductListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ProductFromApi))),
	Schema.mutable(Schema.Array(Product)),
	{
		strict: true,
		decode: (productApiList) => (productApiList ? productApiList : []),
		encode: (productList) => productList,
	},
);

export const CreateProductApi = Schema.Struct({
	name: Schema.String,
	image_url: Schema.optional(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.optional(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.optional(Schema.Number),
});

export const CreateProductApiFromCreateProduct = Schema.transform(
	CreateProduct,
	CreateProductApi,
	{
		strict: true,
		decode: (createProduct) => ({
			...createProduct,
			image_url: createProduct.imageURL,
			commercial_name: createProduct.commercialName,
			sku_code: createProduct.skuCode,
			measurement_unit_id: createProduct.measurementUnitID,
			category_ids: createProduct.categoryIDs,
			brand_id: createProduct.brandID,
			can_be_sold: createProduct.canBeSold,
			can_be_purchased: createProduct.canBePurchased,
			cost_price: createProduct.costPrice,
		}),
		encode: (createProductApi) => ({
			...createProductApi,
			imageURL: createProductApi.image_url,
			commercialName: createProductApi.commercial_name,
			skuCode: createProductApi.sku_code,
			measurementUnitID: createProductApi.measurement_unit_id,
			categoryIDs: createProductApi.category_ids,
			brandID: createProductApi.brand_id,
			canBeSold: createProductApi.can_be_sold,
			canBePurchased: createProductApi.can_be_purchased,
			costPrice: createProductApi.cost_price,
		}),
	},
);

export const UpdateProductApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	image_url: Schema.optional(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.optional(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.optional(Schema.Number),
});

export const UpdateProductApiFromUpdateProduct = Schema.transform(
	UpdateProduct,
	UpdateProductApi,
	{
		strict: true,
		decode: (updateProduct) => ({
			...updateProduct,
			image_url: updateProduct.imageURL,
			commercial_name: updateProduct.commercialName,
			sku_code: updateProduct.skuCode,
			measurement_unit_id: updateProduct.measurementUnitID,
			category_ids: updateProduct.categoryIDs,
			brand_id: updateProduct.brandID,
			can_be_sold: updateProduct.canBeSold,
			can_be_purchased: updateProduct.canBePurchased,
			cost_price: updateProduct.costPrice,
		}),
		encode: (updateProductApi) => ({
			...updateProductApi,
			imageURL: updateProductApi.image_url,
			commercialName: updateProductApi.commercial_name,
			skuCode: updateProductApi.sku_code,
			measurementUnitID: updateProductApi.measurement_unit_id,
			categoryIDs: updateProductApi.category_ids,
			brandID: updateProductApi.brand_id,
			canBeSold: updateProductApi.can_be_sold,
			canBePurchased: updateProductApi.can_be_purchased,
			costPrice: updateProductApi.cost_price,
		}),
	},
);

export const CreateProductApiResponse = Schema.String;
