import { useFieldContext } from "./form";

type Option = { value: string | number; label: string };

interface Props {
	label: string;
	placeholder?: string;
	options: Option[];
	isNumber?: boolean;
}

export function FSSelectField({
	label,
	placeholder,
	options,
	isNumber = false,
}: Props) {
	const field = useFieldContext<number | string>();

	const isTouched = field.state.meta.isTouched;
	const errorsLength = field.state.meta.errors.length;
	const isError = isTouched && errorsLength;
	const isValid = isTouched && !field.state.meta.isValidating;
	const errors = field.state.meta.errors;

	return (
		<fieldset className="fieldset">
			<legend className="fieldset-legend">{label}</legend>
			<select
				className={`select w-full ${isError ? "select-error" : isValid ? "select-success" : ""}`}
				value={isNumber ? field.state.value : field.state.value?.toString()}
				// @ts-ignore
				onChange={(e) =>
					field.handleChange(
						// @ts-ignore
						isNumber ? Number(e.target.value) : e.target.value,
					)
				}
			>
				<option disabled selected>
					{placeholder || "Seleccione una opción"}
				</option>
				{options.map((option) => (
					<option key={option.value} value={option.value}>
						{option.label}
					</option>
				))}
			</select>
			{isError
				? errors.flatMap(({ message }) => (
						<p key={message} className="fieldset-label text-error">
							{message}
						</p>
					))
				: null}
		</fieldset>
	);
}
