import { useQuery } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "src/modules/category/hooks/category-options";
import { CategoryCode } from "src/modules/category/service/model/category";
import { measurementUnitOptions } from "src/modules/measurement-unit/hooks/measurement-unit-options";
import { useUpdateProduct } from "src/modules/product";
import type { Product } from "src/modules/product/service/model/product";
import { CreateProductSchema } from "../CreateProductForm/schema";

export interface EditProductModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	product: Product;
}

export default function useEditProductForm({
	setIsOpen,
	product,
}: EditProductModalProps) {
	const service = useService();
	const { mutate } = useUpdateProduct();

	// Fetch required data for dropdowns
	const { data: brands = [] } = useQuery(brandOptions(service));
	const { data: measurementUnits = [] } = useQuery(
		measurementUnitOptions(service),
	);

	// Get the parent category for products to fetch subcategories
	const { data: categories = [] } = useQuery(categoryOptions(service));
	const productParentCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTS,
	);
	const { data: productSubcategories = [] } = useQuery(
		categorySubcategoriesOptions(service, productParentCategory?.id || ""),
	);

	const form = useAppForm({
		defaultValues: {
			name: product.name,
			commercialName: product.commercialName,
			code: product.code,
			skuCode: product.skuCode,
			brandID: product.brandID,
			measurementUnitID: product.measurementUnitID,
			categoryIDs: product.categoryIDs,
			state: product.state,
			description: product.description || "",
			canBeSold: product.canBeSold,
			canBePurchased: product.canBePurchased,
			costPrice: product.costPrice,
		} as CreateProductSchema,
		validators: {
			onChange: CreateProductSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: product.id,
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: productParentCategory?.id
						? [...value.categoryIDs, productParentCategory?.id]
						: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
				},
				{
					onSuccess: () => {
						toast.success("Producto actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		brands,
		measurementUnits,
		productSubcategories,
	};
}
