import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import { SuppliersTable } from "src/modules/suppliers";

export const Route = createFileRoute("/_authed/admin/products/suppliers/")({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<>
			<div className="container mx-auto max-w-4xl">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<Link
								to="/admin/products/suppliers/create"
								className="btn btn-primary"
							>
								<Plus size={16} />
								Crear nuevo insumo
							</Link>
						</div>
					</div>
					<SuppliersTable />
				</div>
			</div>
		</>
	);
}
