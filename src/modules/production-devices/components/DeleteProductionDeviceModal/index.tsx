import { toast } from "react-toastify";
import { cn } from "~/core/utils/classes";
import { getErrorResult } from "~/core/utils/effectErrors";
import useDeleteProduct from "~/modules/product/hooks/use-delete-product";
import type { Product } from "~/modules/product/service/model/product";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	product: Product;
}

export default function DeleteProductionDeviceModal({
	isOpen,
	setIsOpen,
	product,
}: Props) {
	const { mutate, isPending } = useDeleteProduct();

	const handleDelete = () => {
		mutate(product.id, {
			onSuccess: () => {
				toast.success("Dispositivo de producción eliminado");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<h3 className="font-bold text-lg">
					Eliminar Dispositivo de Producción
				</h3>
				<p className="py-4">
					¿Estás seguro de que deseas eliminar el dispositivo{" "}
					<strong>{product.name}</strong>? Esta acción no se puede deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? (
							<span className="loading loading-spinner loading-sm" />
						) : (
							"Eliminar"
						)}
					</button>
				</div>
			</div>
		</div>
	);
}
