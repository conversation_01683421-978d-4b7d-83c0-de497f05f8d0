import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import type { CreateProductionFlow, ProductionFlowCreateWithActivities, UpdateProductionFlow } from "../../model/production-flow";
import { ProductionFlowRepository } from "../../model/repository";
import {
	CreateProductionFlowApiFromCreateProductionFlow,
	CreateProductionFlowApiResponse,
	ProductionFlowCreateWithActivitiesApiFromProductionFlowCreateWithActivities,
	ProductionFlowFromApi,
	ProductionFlowListFromApi,
	ProductionFlowWithActivitiesFromApi,
	UpdateProductionFlowApiFromUpdateProductionFlow,
} from "./dto";

const baseUrl = "/v1/production-flows";

const makeProductionFlowApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ProductionFlowListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ProductionFlowFromApi))),
		create: (productionFlow: CreateProductionFlow) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							CreateProductionFlowApiFromCreateProductionFlow,
						)(productionFlow),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateProductionFlowApiResponse))),
		createWithActivities: (
			productionFlow: ProductionFlowCreateWithActivities,
		) =>
			httpClient
				.post(`${baseUrl}/with-activities`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							ProductionFlowCreateWithActivitiesApiFromProductionFlowCreateWithActivities,
						)(productionFlow),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateProductionFlowApiResponse))),
		update: (productionFlow: UpdateProductionFlow) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							UpdateProductionFlowApiFromUpdateProductionFlow,
						)(productionFlow),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		getWithActivities: (id: string) =>
			httpClient
				.get(`${baseUrl}/activities/${id}`)
				.pipe(
					Effect.flatMap(handleDResponse(ProductionFlowWithActivitiesFromApi)),
				),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const productionFlowApiRepoLive = Layer.effect(
	ProductionFlowRepository,
	makeProductionFlowApiRepo,
);
