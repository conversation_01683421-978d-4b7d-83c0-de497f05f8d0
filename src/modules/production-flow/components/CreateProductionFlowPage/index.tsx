import { Hash, Plus, Tag } from "lucide-react";
import { useState } from "react";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import ActivitiesTable from "../ActivitiesTable";
import AddActivityModal from "../AddActivityModal";
import type { ActivitySchema } from "../schemas";
import useCreateProductionFlowPage from "./use-create-production-flow-page";

interface CreateProductionFlowFormProps {
	activities: ActivitySchema[];
	setActivities: React.Dispatch<React.SetStateAction<ActivitySchema[]>>;
}

export default function CreateProductionFlowForm({
	activities,
	setActivities,
}: CreateProductionFlowFormProps) {
	const { productionFlow } = useService();
	const [isAddActivityOpen, setIsAddActivityOpen] = useState(false);

	const { form, handleSubmit, isPending } = useCreateProductionFlowPage({
		activities,
	});

	const handleAddActivity = (activity: ActivitySchema) => {
		setActivities((prev) => [...prev, activity]);
	};

	const handleReorderActivities = (reorderedActivities: ActivitySchema[]) => {
		setActivities(reorderedActivities);
	};

	const handleDeleteActivity = (tempId: string) => {
		setActivities((prev) =>
			prev.filter((activity) => activity.tempId !== tempId),
		);
	};

	const nextIndexNumber = activities.length + 1;

	return (
		<>
			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Flujo</h2>
						<form.AppForm>
							<fieldset className="fieldset">
								<form.AppField
									name="name"
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre"
											placeholder="Nombre del flujo de producción"
											prefixComponent={<Tag size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="code"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (!value || value.trim() === "") {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(
													productionFlow.validateCode(value),
												);
												return undefined;
											} catch (e) {
												return [{ message: "El código ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Código"
											placeholder="Código del flujo de producción"
											prefixComponent={<Hash size={16} />}
										/>
									)}
								/>
							</fieldset>
						</form.AppForm>
					</div>
				</div>

				{/* Activities Section */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="font-semibold text-xl">Actividades</h2>
						<button
							type="button"
							className="btn btn-primary btn-sm"
							onClick={() => setIsAddActivityOpen(true)}
						>
							<Plus size={16} />
							Agregar Actividad
						</button>
					</div>

					<ActivitiesTable
						activities={activities}
						onReorder={handleReorderActivities}
						onDelete={handleDeleteActivity}
					/>
				</div>

				{/* Submit Button */}
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<div className="flex items-center justify-end gap-4">
							{activities.length === 0 && (
								<p className="text-error text-sm">
									Debe agregar al menos una actividad antes de crear el flujo de
									producción.
								</p>
							)}
							<a
								href="/admin/manufacture/production-flow"
								className="btn btn-ghost"
							>
								Cancelar
							</a>
							<button
								type="button"
								className="btn btn-primary"
								onClick={handleSubmit}
								disabled={isPending || activities.length === 0}
							>
								{isPending ? (
									<span className="loading loading-spinner loading-sm" />
								) : (
									"Crear Flujo de Producción"
								)}
							</button>
						</div>
					</div>
				</div>
			</div>
			<AddActivityModal
				isOpen={isAddActivityOpen}
				setIsOpen={setIsAddActivityOpen}
				onAddActivity={handleAddActivity}
				nextIndexNumber={nextIndexNumber}
			/>
		</>
	);
}
