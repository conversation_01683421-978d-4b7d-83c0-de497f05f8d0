import { useQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import EditProductForm from "src/modules/product/components/EditProductPage";
import { productOptionsById } from "src/modules/product/hooks/product-options";

export const Route = createFileRoute(
	"/_authed/admin/products/products/edit/$id",
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { id } = Route.useParams();
	const service = useService();

	// Fetch product data
	const { data, isLoading, error } = useQuery(
		productOptionsById(service, id),
	);

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isLoading) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/products/products"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">
						Editar Producto
					</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">
							Información del Producto
						</h2>
						<EditProductForm product={data} />
					</div>
				</div>
			</div>
		</div>
	);
}
