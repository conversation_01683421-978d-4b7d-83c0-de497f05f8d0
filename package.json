{"name": "fhyona-v2-frontend", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build"}, "private": true, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/bun": "^1.2.15", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "daisyui": "^5.0.43", "vite-tsconfig-paths": "^5.1.4"}, "peerDependencies": {"typescript": "^5.8.3"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@effect/platform": "^0.84.9", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-form": "^1.12.2", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-router": "^1.121.0", "@tanstack/react-router-devtools": "^1.121.0", "@tanstack/react-router-with-query": "^1.121.0", "@tanstack/react-start": "^1.121.0", "@tanstack/react-store": "^0.7.1", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "downshift": "^9.0.9", "effect": "^3.16.5", "lucide-react": "^0.514.0", "mutative": "^1.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-toastify": "^11.0.5", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "use-mutative": "^1.3.0", "valibot": "^1.1.0", "vinxi": "^0.5.7", "vite": "^6.3.5"}}