import { Http<PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import { handleDResponse, handleResponse } from "src/core/service/repo/api/utils";
import type { CreateMeasurementUnit, UpdateMeasurementUnit } from "../../model/measurement-unit";
import { MeasurementUnitRepository } from "../../model/repository";
import { CreateMeasurementUnitApiFromCreateMeasurementUnit, CreateMeasurementUnitApiResponse, MeasurementUnitFromApi, MeasurementUnitListFromApi, UpdateMeasurementUnitApiFromUpdateMeasurementUnit } from "./dto";

const baseUrl = "/v1/measurement-units";

const makeMeasurementUnitApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(MeasurementUnitListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(MeasurementUnitFromApi))),
		create: (measurementUnit: CreateMeasurementUnit) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateMeasurementUnitApiFromCreateMeasurementUnit)(measurementUnit),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateMeasurementUnitApiResponse))),
		update: (measurementUnit: UpdateMeasurementUnit) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateMeasurementUnitApiFromUpdateMeasurementUnit)(measurementUnit),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const measurementUnitApiRepoLive = Layer.effect(MeasurementUnitRepository, makeMeasurementUnitApiRepo);
