// Product hooks exports
export { default as useProducts } from "./hooks/use-products";
export { default as useProduct } from "./hooks/use-product";
export { default as useCreateProduct } from "./hooks/use-create-product";
export { default as useUpdateProduct } from "./hooks/use-update-product";
export { default as useDeleteProduct } from "./hooks/use-delete-product";
export { default as useValidateCode } from "./hooks/use-validate-code";
export { default as useValidateCommercialName } from "./hooks/use-validate-commercial-name";
export { default as useValidateSKUCode } from "./hooks/use-validate-sku-code";
export { default as useProductsByCategory } from "./hooks/use-products-by-category";

// Product options exports
export {
	productOptions,
	productOptionsById,
	productOptionsByCategoryCode,
} from "./hooks/product-options";

// Product types exports
export type {
	Product,
	CreateProduct,
	UpdateProduct,
} from "./service/model/product";

// Product components exports
export { default as ProductsTable } from "./components/ProductsTable";
export { default as CreateProductForm } from "./components/CreateProductForm";
