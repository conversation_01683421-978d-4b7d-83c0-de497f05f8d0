import type { QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import {
	HeadContent,
	Outlet,
	Scripts,
	createRootRouteWithContext,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import type { ReactNode } from "react";
import { ToastContainer } from "react-toastify";
import { ServiceProvider } from "src/config/context/serviceProvider";
import { ThemeProvider, useTheme } from "src/config/context/themeProvider";
import { serviceRegistry } from "src/core/service";
import { getThemeServerFn } from "src/modules/auth/server/theme";
// @ts-ignore
import appCss from "../config/css/app.css?url";

export const Route = createRootRouteWithContext<{
	queryClient: QueryClient;
}>()({
	head: () => ({
		meta: [
			{
				charSet: "utf-8",
			},
			{
				name: "viewport",
				content: "width=device-width, initial-scale=1",
			},
			{
				title: "TanStack Start Starter",
			},
		],
		links: [
			{
				rel: "stylesheet",
				href: appCss,
			},
		],
	}),
	loader: () => getThemeServerFn(),
	component: RootComponent,
});

function RootComponent() {
	const data = Route.useLoaderData();

	return (
		<ThemeProvider theme={data}>
			<RootDocument>
				<ServiceProvider service={serviceRegistry}>
					<Outlet />
				</ServiceProvider>
			</RootDocument>
		</ThemeProvider>
	);
}

function RootDocument({ children }: Readonly<{ children: ReactNode }>) {
	const { theme } = useTheme();

	return (
		<html lang="es" data-theme={theme} suppressHydrationWarning>
			<head>
				<HeadContent />
			</head>
			<body>
				{children}
				<ToastContainer theme="dark" />
				<ReactQueryDevtools />
				<TanStackRouterDevtools />
				<Scripts />
			</body>
		</html>
	);
}
