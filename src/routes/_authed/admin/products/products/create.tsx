import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import CreateProductForm from "src/modules/product/components/CreateProductPage";

export const Route = createFileRoute("/_authed/admin/products/products/create")(
	{
		component: RouteComponent,
	},
);

function RouteComponent() {
	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link to="/admin/products/products" className="btn btn-ghost btn-sm">
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Crear Producto</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Producto</h2>
						<CreateProductForm />
					</div>
				</div>
			</div>
		</div>
	);
}
